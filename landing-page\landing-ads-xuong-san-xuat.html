<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Xưởng Sản Xuất Túi Vải In Logo #1 Việt Nam - Loma Bag</title>
    <meta
      name="description"
      content="Xưởng sản xuất túi vải in logo chuyên nghiệp nhất Việt Nam. 1 triệu+ túi đã sản xuất cho 500+ shop. Từ 50 túi, giao 7 ngày, bảo hành 30 ngày. Miễn phí thiết kế & mẫu."
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- SwiperJS for Gallery Slider -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/swiper/swiper-bundle.min.css"
    />

    <style>
      /* Custom Styles */
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Be Vietnam Pro", sans-serif;
        background-color: #f9fafb;
        line-height: 1.7;
      }
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        line-height: 1.4;
      }
      .cta-button {
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }
      .cta-button:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
      }
      .section-title {
        position: relative;
        padding-bottom: 1rem;
      }
      .section-title::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 4px;
        background-color: #ef4444;
        border-radius: 2px;
      }

      /* Enhanced Hero Section */
      .hero-gradient {
        background: linear-gradient(135deg, #ea580c 0%, #f97316 50%, #fb923c 100%);
        position: relative;
        overflow: hidden;
      }
      .hero-gradient::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        opacity: 0.5;
      }
      .hero-gradient::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 25%),
                    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 25%);
      }

      /* Floating animation */
      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }
      .float-animation {
        animation: float 3s ease-in-out infinite;
      }

      /* Factory showcase animations */
      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
      
      @keyframes slideInRight {
        from {
          opacity: 0;
          transform: translateX(50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
      
      .slide-in-left {
        animation: slideInLeft 0.8s ease-out;
      }
      
      .slide-in-right {
        animation: slideInRight 0.8s ease-out;
      }

      /* Pulse effect for stats */
      @keyframes pulse-stat {
        0%, 100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }
      
      .stat-card:hover {
        animation: pulse-stat 0.6s ease-in-out;
      }

      /* Video Section Styles */
      .video-placeholder {
        aspect-ratio: 16 / 9;
        background-color: #111827; /* gray-900 */
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.75rem;
        cursor: pointer;
        overflow: hidden;
        position: relative;
      }
      .play-button {
        width: 80px;
        height: 80px;
        background-color: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(5px);
        border: 2px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s ease, background-color 0.2s ease;
        z-index: 10;
      }
      .play-button:hover {
        transform: scale(1.1);
        background-color: rgba(239, 68, 68, 0.8);
      }
      .video-placeholder img {
        transition: transform 0.5s ease;
      }
      .video-placeholder:hover img {
        transform: scale(1.05);
      }

      /* Modal */
      .modal {
        transition: opacity 0.3s ease;
        z-index: 9999;
      }
      .modal.show {
        display: flex !important;
        opacity: 1 !important;
      }
      .modal.hide {
        opacity: 0 !important;
      }

      /* Zalo Sticky Button */
      .zalo-sticky {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #0068ff, #0052cc);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 20px rgba(0, 104, 255, 0.4);
        transition: all 0.3s ease;
        animation: pulse 2s infinite;
      }

      .zalo-sticky:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(0, 104, 255, 0.6);
      }

      @keyframes pulse {
        0% {
          box-shadow: 0 4px 20px rgba(0, 104, 255, 0.4);
        }
        50% {
          box-shadow: 0 4px 20px rgba(0, 104, 255, 0.8);
        }
        100% {
          box-shadow: 0 4px 20px rgba(0, 104, 255, 0.4);
        }
      }
    </style>
  </head>
  <body class="text-gray-800">
    <!-- HEADER -->
    <header
      class="bg-white/95 backdrop-blur-lg fixed top-0 left-0 right-0 z-50 shadow-sm border-b border-gray-100"
    >
      <div class="container mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
          <!-- Logo & Badge -->
          <div class="flex items-center">
            <a href="#" class="flex items-center">
              <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma//logo-full.png" alt="Loma Bag Logo" class="h-10">
            </a>
            <span
              class="ml-2 md:ml-3 bg-red-100 text-red-600 text-xs font-bold px-2 py-1 rounded-full"
              >Xưởng Sản Xuất</span
            >
          </div>

          <!-- Desktop Navigation -->
          <nav class="hidden md:flex items-center space-x-6 lg:space-x-8">
            <a
              href="#factory-showcase"
              class="text-gray-600 hover:text-red-500 font-medium transition-colors text-sm lg:text-base"
              >Xưởng Sản Xuất</a
            >
            <a
              href="#gallery"
              class="text-gray-600 hover:text-red-500 font-medium transition-colors text-sm lg:text-base"
              >Mẫu Túi</a
            >
            <a
              href="#case-study"
              class="text-gray-600 hover:text-red-500 font-medium transition-colors text-sm lg:text-base"
              >Case Study</a
            >
            <a
              href="#pricing"
              class="text-gray-600 hover:text-red-500 font-medium transition-colors text-sm lg:text-base"
              >Bảng Giá</a
            >
          </nav>

          <!-- Desktop CTA -->
          <div class="hidden md:flex items-center space-x-3 lg:space-x-4">
            <div class="hidden lg:flex items-center text-sm text-gray-600">
              <a href="https://zalo.me/0938069715" target="_blank" class="flex items-center hover:text-blue-600 transition-colors">
                <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma//zalo-icon.png" alt="Zalo" class="w-4 h-4 mr-1">
                <span class="font-semibold">0938069715</span>
              </a>
            </div>
            <a
              href="#form-7uTqf4pFme"
              class="cta-button bg-red-500 text-white font-bold py-2 px-4 lg:px-6 rounded-full hover:bg-red-600 text-sm"
            >
              🎁 Báo Giá
            </a>
          </div>

          <!-- Mobile CTA -->
          <a
            href="#form-7uTqf4pFme"
            class="md:hidden cta-button bg-red-500 text-white font-bold py-2 px-4 rounded-full hover:bg-red-600 text-sm"
          >
            Báo Giá
          </a>
        </div>
      </div>
    </header>

    <!-- HERO SECTION -->
    <section class="hero-gradient text-white pt-32 pb-20 text-center relative">
      <div class="container mx-auto px-6 relative z-10">
        <!-- Floating icons -->
        <div class="absolute top-10 left-10 float-animation hidden lg:block">
          <div
            class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"
          >
            <span class="text-2xl">🏭</span>
          </div>
        </div>
        <div
          class="absolute top-20 right-20 float-animation hidden lg:block"
          style="animation-delay: 1s"
        >
          <div
            class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"
          >
            <span class="text-xl">🎯</span>
          </div>
        </div>
        <div
          class="absolute bottom-10 left-20 float-animation hidden lg:block"
          style="animation-delay: 2s"
        >
          <div
            class="w-14 h-14 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"
          >
            <span class="text-xl">🛍️</span>
          </div>
        </div>

        <div class="max-w-5xl mx-auto">
          <!-- Badge giá trị -->
          <div class="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-6 border border-white/30">
            <span class="text-yellow-300 mr-2">⭐</span>
            <span class="text-white font-semibold">Từ 50 túi - Giao trong 7 ngày - Bảo hành 30 ngày</span>
            <span class="text-yellow-300 ml-2">⭐</span>
          </div>

          <h1 class="text-4xl md:text-6xl font-extrabold leading-[1.2] mb-8">
            <span class="text-white">Xưởng Sản Xuất</span><br />
            <span class="text-yellow-100">TÚI VẢI IN LOGO</span><br />
            <span class="text-blue-100">Giúp Shop Tăng Doanh Thu</span>
          </h1>

          <!-- Value proposition chính -->
          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20">
            <p class="text-xl md:text-2xl font-semibold text-white mb-4">
              🎁 Miễn phí thiết kế - Ship toàn quốc - Làm mẫu miễn phí
            </p>
            <p class="text-lg md:text-xl text-white/90">
              Túi vải in logo làm <strong class="text-blue-100">quà tặng khách hàng</strong> giúp
              <strong class="text-yellow-100">tăng 15-30% tỷ lệ chốt đơn</strong>.
              Đã có <strong class="text-white">500+ shop tin tưởng</strong>
              với hơn 1 triệu túi đã sản xuất!
            </p>
          </div>

          <!-- Showcase nhanh các mẫu túi -->
          <div class="flex flex-wrap justify-center gap-4 mb-10 max-w-3xl mx-auto">
            <div class="bg-white/20 backdrop-blur-sm rounded-lg p-3 border border-white/30 hover:bg-white/30 transition-all duration-300 cursor-pointer flex-shrink-0 w-20 text-center">
              <div class="text-2xl mb-1">👜</div>
              <div class="text-xs text-white/90 font-medium">Túi Tote</div>
            </div>
            <div class="bg-white/20 backdrop-blur-sm rounded-lg p-3 border border-white/30 hover:bg-white/30 transition-all duration-300 cursor-pointer flex-shrink-0 w-20 text-center">
              <div class="text-2xl mb-1">🎒</div>
              <div class="text-xs text-white/90 font-medium">Túi Dây Rút</div>
            </div>
            <div class="bg-white/20 backdrop-blur-sm rounded-lg p-3 border border-white/30 hover:bg-white/30 transition-all duration-300 cursor-pointer flex-shrink-0 w-20 text-center">
              <div class="text-2xl mb-1">📦</div>
              <div class="text-xs text-white/90 font-medium">Túi Hộp</div>
            </div>
            <div class="bg-white/20 backdrop-blur-sm rounded-lg p-3 border border-white/30 hover:bg-white/30 transition-all duration-300 cursor-pointer flex-shrink-0 w-20 text-center">
              <div class="text-2xl mb-1">🛍️</div>
              <div class="text-xs text-white/90 font-medium">Túi Quai</div>
            </div>
            <div class="bg-white/20 backdrop-blur-sm rounded-lg p-3 border border-white/30 hover:bg-white/30 transition-all duration-300 cursor-pointer flex-shrink-0 w-20 text-center">
              <div class="text-2xl mb-1">✨</div>
              <div class="text-xs text-white/90 font-medium">Cao Cấp</div>
            </div>
          </div>

          <!-- CTA buttons -->
          <div class="flex flex-col sm:flex-row justify-center items-center gap-4">
            <a
              href="#form-7uTqf4pFme"
              class="cta-button bg-red-500 text-white font-bold py-4 px-8 rounded-full text-lg hover:bg-red-600 inline-block w-full sm:w-auto shadow-2xl"
            >
              🎁 Nhận Báo Giá & Mẫu Miễn Phí
            </a>
            <a
              href="#gallery"
              class="cta-button bg-white/30 backdrop-blur-sm text-white border border-white/40 font-bold py-4 px-8 rounded-full text-lg hover:bg-white/40 inline-block w-full sm:w-auto"
            >
              🎨 Xem 1000+ Mẫu Túi
            </a>
          </div>

          <!-- Trust indicators -->
          <div class="mt-8 flex flex-wrap justify-center items-center gap-6 text-white/80">
            <div class="flex items-center">
              <span class="text-green-400 mr-2">✅</span>
              <span class="text-sm">Miễn phí thiết kế</span>
            </div>
            <div class="flex items-center">
              <span class="text-green-400 mr-2">✅</span>
              <span class="text-sm">Ship toàn quốc</span>
            </div>
            <div class="flex items-center">
              <span class="text-green-400 mr-2">✅</span>
              <span class="text-sm">Bảo hành 30 ngày</span>
            </div>
            <div class="flex items-center">
              <span class="text-green-400 mr-2">✅</span>
              <span class="text-sm">Làm mẫu miễn phí</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FACTORY SHOWCASE SECTION -->
    <section id="factory-showcase" class="py-16 md:py-20 bg-white relative">
      <div class="container mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-12">
          <div class="inline-flex items-center bg-blue-100 text-blue-800 rounded-full px-4 py-2 mb-4">
            <span class="mr-2">🏭</span>
            <span class="font-semibold text-sm">Tham quan xưởng sản xuất</span>
          </div>
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Tại Sao 500+ Shop Chọn Chúng Tôi?
          </h2>
          <p class="text-lg text-gray-600 max-w-3xl mx-auto">
            Không chỉ sản xuất túi vải, chúng tôi giúp shop tăng doanh thu bền vững với túi vải in logo làm quà tặng khách hàng
          </p>
        </div>

        <!-- Factory Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <div class="stat-card text-center p-6 bg-gradient-to-br from-red-50 to-pink-50 rounded-xl border border-red-100 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="text-3xl md:text-5xl font-bold text-red-600 mb-2">1M+</div>
            <div class="text-sm md:text-base text-gray-700 font-medium">Túi đã sản xuất</div>
            <div class="text-xs text-gray-500 mt-1">Từ 2019 đến nay</div>
          </div>
          <div class="stat-card text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="text-3xl md:text-5xl font-bold text-blue-600 mb-2">500+</div>
            <div class="text-sm md:text-base text-gray-700 font-medium">Shop đối tác</div>
            <div class="text-xs text-gray-500 mt-1">Trên toàn quốc</div>
          </div>
          <div class="stat-card text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="text-3xl md:text-5xl font-bold text-green-600 mb-2">7</div>
            <div class="text-sm md:text-base text-gray-700 font-medium">Ngày giao hàng</div>
            <div class="text-xs text-gray-500 mt-1">Cam kết thời gian</div>
          </div>
          <div class="stat-card text-center p-6 bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl border border-purple-100 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="text-3xl md:text-5xl font-bold text-purple-600 mb-2">98%</div>
            <div class="text-sm md:text-base text-gray-700 font-medium">Khách hài lòng</div>
            <div class="text-xs text-gray-500 mt-1">Đánh giá 5 sao</div>
          </div>
        </div>

        <!-- Factory Capabilities -->
        <div class="grid md:grid-cols-2 gap-12 items-center mb-16">
          <div class="slide-in-left">
            <h3 class="text-2xl md:text-3xl font-bold text-gray-800 mb-6">
              🎯 Bạn Sẽ Nhận Được Gì?
            </h3>
            <div class="space-y-4">
              <div class="flex items-start">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span class="text-white text-sm">✓</span>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-800 mb-1">Chất lượng đồng đều 100%</h4>
                  <p class="text-gray-600 text-sm">Máy móc hiện đại, QC 3 lần đảm bảo từng chiếc túi đều đẹp như mẫu</p>
                </div>
              </div>
              <div class="flex items-start">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span class="text-white text-sm">✓</span>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-800 mb-1">Giao hàng đúng hẹn</h4>
                  <p class="text-gray-600 text-sm">Cam kết giao trong 7 - 15 ngày, đội ngũ 20+ thợ lành nghề sản xuất nhanh</p>
                </div>
              </div>
              <div class="flex items-start">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span class="text-white text-sm">✓</span>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-800 mb-1">Giá tốt nhất thị trường</h4>
                  <p class="text-gray-600 text-sm">Giá gốc xưởng, không qua trung gian, tiết kiệm 20-30% so với ngoài</p>
                </div>
              </div>
              <div class="flex items-start">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span class="text-white text-sm">✓</span>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-800 mb-1">Hỗ trợ tận tình</h4>
                  <p class="text-gray-600 text-sm">Tư vấn chọn mẫu, thiết kế miễn phí, hỗ trợ 24/7 từ đặt hàng đến nhận túi</p>
                </div>
              </div>
            </div>
          </div>
          <div class="relative slide-in-right">
            <img
              src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/nha-xuong/hinh-anh-xuong-tui-vaui-loma.jpg"
              alt="Xưởng sản xuất túi vải Loma Bag"
              class="w-full h-80 object-cover rounded-2xl shadow-xl"
            >
            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-2xl"></div>
            <div class="absolute bottom-6 left-6 text-white">
              <div class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold inline-block mb-2">
                🔴 LIVE
              </div>
              <h4 class="text-lg font-bold">Xưởng đang hoạt động</h4>
              <p class="text-sm text-white/90">Cập nhật realtime từ nhà máy</p>
            </div>
          </div>
        </div>

        <!-- CTA Section -->
        <div class="text-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
          <h3 class="text-2xl font-bold text-gray-800 mb-4">
            🎁 Muốn Xem Trực Tiếp Chất Lượng Sản Phẩm?
          </h3>
          <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
            Chúng tôi sẽ làm 1 túi mẫu thực tế với logo của bạn. Cọc 500k, nếu đặt từ 100 túi sẽ hoàn lại 100%
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#form-7uTqf4pFme"
              class="cta-button bg-red-500 text-white font-bold py-3 px-8 rounded-full hover:bg-red-600 inline-block"
            >
              🎯 Yêu Cầu Làm Mẫu Ngay
            </a>
            <a
              href="#video-section"
              class="cta-button bg-white text-blue-600 border-2 border-blue-600 font-bold py-3 px-8 rounded-full hover:bg-blue-50 inline-block"
            >
              📹 Xem Video Xưởng
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- VIDEO SECTION -->
    <section
      id="video-section"
      class="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-blue-50"
    >
      <div class="container mx-auto px-6">
        <div class="text-center mb-12">
          <h3 class="section-title text-3xl md:text-4xl font-bold">
            📹 Trăm Nghe Không Bằng Một Thấy
          </h3>
          <p class="mt-4 text-gray-600 max-w-3xl mx-auto text-lg">
            Xem video thực tế về quy trình sản xuất và chất lượng sản phẩm. Đây
            là bằng chứng cụ thể về sự chuyên nghiệp của Loma Bag.
          </p>
        </div>

        <div class="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto mb-12">
          <!-- Video 1: Product Showcase -->
          <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div class="video-placeholder" data-video-id="jNoM_JdWVcLplv1Q">
              <img
                src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-day-in-logo/tui-vai-day-quai-phoi-mau-loma-bag-1.jpg"
                alt="Các mẫu túi vải in logo hot nhất"
                class="absolute inset-0 w-full h-full object-cover"
              />
              <div class="absolute inset-0 bg-black/30"></div>
              <div class="play-button">
                <svg
                  class="w-8 h-8 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <div class="absolute bottom-4 left-4 right-4 text-white">
                <div
                  class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold inline-block mb-2"
                >
                  🔥 Trending
                </div>
                <h4 class="text-lg font-bold">
                  Top Các Mẫu Túi Tại Loma Bag
                </h4>
                <p class="text-sm text-white/90">
                  Xem các mẫu túi được shop yêu thích nhất
                </p>
              </div>
            </div>
          </div>

          <!-- Video 2: Factory Tour -->
          <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div class="video-placeholder" data-video-id="jE1IKW0p0AM">
              <img
                src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/nha-xuong/hinh-anh-xuong-tui-vaui-loma.jpg"
                alt="Tham quan xưởng sản xuất Loma Bag"
                class="absolute inset-0 w-full h-full object-cover"
              />
              <div class="absolute inset-0 bg-black/30"></div>
              <div class="play-button">
                <svg
                  class="w-8 h-8 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <div class="absolute bottom-4 left-4 right-4 text-white">
                <div
                  class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold inline-block mb-2"
                >
                  🏭 Behind The Scenes
                </div>
                <h4 class="text-lg font-bold">Tham Quan Xưởng Sản Xuất</h4>
                <p class="text-sm text-white/90">
                  Quy trình sản xuất chuyên nghiệp từ A-Z
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Video Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
          <div class="text-center p-4 bg-white rounded-lg shadow-md">
            <div class="text-2xl font-bold text-red-600">1Triệu+</div>
            <div class="text-sm text-gray-600">Túi đã sản xuất</div>
          </div>
          <div class="text-center p-4 bg-white rounded-lg shadow-md">
            <div class="text-2xl font-bold text-blue-600">98%</div>
            <div class="text-sm text-gray-600">Khách hài lòng</div>
          </div>
          <div class="text-center p-4 bg-white rounded-lg shadow-md">
            <div class="text-2xl font-bold text-green-600">10 phút</div>
            <div class="text-sm text-gray-600">Phản hồi nhanh</div>
          </div>
          <div class="text-center p-4 bg-white rounded-lg shadow-md">
            <div class="text-2xl font-bold text-purple-600">500+</div>
            <div class="text-sm text-gray-600">Shop tin tưởng</div>
          </div>
        </div>
      </div>
    </section>

    <!-- GALLERY SECTION -->
    <section id="gallery" class="py-16 md:py-24 bg-gray-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-12">
          <div class="inline-flex items-center bg-green-100 text-green-800 rounded-full px-4 py-2 mb-4">
            <span class="mr-2">🎨</span>
            <span class="font-semibold text-sm">Thư viện sản phẩm</span>
          </div>
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            1000+ Mẫu Túi Vải Đã Sản Xuất
          </h2>
          <p class="text-lg text-gray-600 max-w-3xl mx-auto">
            Từ túi tote đơn giản đến túi cao cấp phức tạp, chúng tôi đã sản xuất thành công cho mọi ngành hàng
          </p>
        </div>

        <!-- Category Tabs -->
        <div class="flex flex-wrap justify-center gap-4 mb-12">
          <button class="category-tab active bg-red-500 text-white px-6 py-2 rounded-full font-semibold" data-category="all">
            Tất cả mẫu
          </button>
          <button class="category-tab bg-white text-gray-700 px-6 py-2 rounded-full font-semibold border border-gray-300" data-category="tote">
            Túi Tote
          </button>
          <button class="category-tab bg-white text-gray-700 px-6 py-2 rounded-full font-semibold border border-gray-300" data-category="drawstring">
            Túi Dây Rút
          </button>
          <button class="category-tab bg-white text-gray-700 px-6 py-2 rounded-full font-semibold border border-gray-300" data-category="box">
            Túi Hộp
          </button>
          <button class="category-tab bg-white text-gray-700 px-6 py-2 rounded-full font-semibold border border-gray-300" data-category="premium">
            Cao Cấp
          </button>
        </div>

        <!-- Gallery Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <!-- Túi Tote Canvas -->
          <div class="gallery-item bg-white rounded-2xl shadow-lg overflow-hidden group" data-category="tote">
            <div class="relative aspect-square">
              <img
                src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-canvas-hop.png"
                alt="Túi tote canvas in logo"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
              <div class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                🔥 Phổ biến nhất
              </div>
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="absolute bottom-4 left-4 right-4 text-white">
                  <h4 class="text-lg font-bold mb-1">Túi Tote Canvas</h4>
                  <p class="text-sm text-white/90 mb-2">Phù hợp: Shop ngân sách ít, quà tặng đại trà</p>
                  <div class="flex items-center text-yellow-400 text-sm">
                    <span class="mr-2">⭐⭐⭐⭐⭐</span>
                    <span class="text-white">500+ shop đã chọn</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="p-6">
              <h4 class="font-bold text-gray-800 mb-2">Túi Tote Canvas</h4>
              <p class="text-gray-600 text-sm mb-3">Chất liệu canvas dày dặn, in logo sắc nét</p>
              <div class="flex justify-between items-center">
                <span class="text-red-600 font-bold">Từ 25k - 65k</span>
                <span class="text-gray-500 text-sm">MOQ: 50 túi</span>
              </div>
            </div>
          </div>

          <!-- Túi Dây Rút -->
          <div class="gallery-item bg-white rounded-2xl shadow-lg overflow-hidden group" data-category="drawstring">
            <div class="relative aspect-square">
              <img
                src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-day-in-logo/tui-vai-day-linen-in-chuyen-nhiet-full-tui-loma-bag-1.png"
                alt="Túi vải đay linen dạng Hộp Đứng"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
              <div class="absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                💪 Thời trang
              </div>
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="absolute bottom-4 left-4 right-4 text-white">
                  <h4 class="text-lg font-bold mb-1">Túi Đay Linen</h4>
                  <p class="text-sm text-white/90 mb-2">Phù hợp: Shop thời trang, phụ kiện, gym</p>
                  <div class="flex items-center text-yellow-400 text-sm">
                    <span class="mr-2">⭐⭐⭐⭐⭐</span>
                    <span class="text-white">300+ shop đã chọn</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="p-6">
              <h4 class="font-bold text-gray-800 mb-2">Túi Đay Linen</h4>
              <p class="text-gray-600 text-sm mb-3">Tiện lợi, gọn nhẹ, phù hợp mọi lứa tuổi</p>
              <div class="flex justify-between items-center">
                <span class="text-red-600 font-bold">Từ 45k-85k</span>
                <span class="text-gray-500 text-sm">MOQ: 50 túi</span>
              </div>
            </div>
          </div>

          <!-- Túi Hộp Cao Cấp -->
          <div class="gallery-item bg-white rounded-2xl shadow-lg overflow-hidden group" data-category="premium">
            <div class="relative aspect-square">
              <img
                src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-quai-phoi-mau.png"
                alt="Túi hộp cao cấp"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
              <div class="absolute top-4 left-4 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                👑 Cao cấp
              </div>
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="absolute bottom-4 left-4 right-4 text-white">
                  <h4 class="text-lg font-bold mb-1">Túi Hộp Phối Màu</h4>
                  <p class="text-sm text-white/90 mb-2">Phù hợp: Shop mỹ phẩm, trang sức cao cấp</p>
                  <div class="flex items-center text-yellow-400 text-sm">
                    <span class="mr-2">⭐⭐⭐⭐⭐</span>
                    <span class="text-white">200+ shop đã chọn</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="p-6">
              <h4 class="font-bold text-gray-800 mb-2">Túi Hộp Phối Màu (BST Hạ)</h4>
              <p class="text-gray-600 text-sm mb-3">Thiết kế sang trọng, tạo ấn tượng mạnh</p>
              <div class="flex justify-between items-center">
                <span class="text-red-600 font-bold">Từ 45k-85k</span>
                <span class="text-gray-500 text-sm">MOQ: 50 túi</span>
              </div>
            </div>
          </div>

          <!-- Túi Tote Phối Quai -->
          <div class="gallery-item bg-white rounded-2xl shadow-lg overflow-hidden group" data-category="tote">
            <div class="relative aspect-square">
              <img
                src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-day-in-logo/tui-vai-day-quai-phoi-mau-loma-bag-1.jpg"
                alt="Túi tote phối quai"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
              <div class="absolute top-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                ✨ Đẹp mắt
              </div>
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="absolute bottom-4 left-4 right-4 text-white">
                  <h4 class="text-lg font-bold mb-1">Túi Hộp Phối Màu (BST Hạ)</h4>
                  <p class="text-sm text-white/90 mb-2">Phù hợp: Shop thời trang, lifestyle</p>
                  <div class="flex items-center text-yellow-400 text-sm">
                    <span class="mr-2">⭐⭐⭐⭐⭐</span>
                    <span class="text-white">250+ shop đã chọn</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="p-6">
              <h4 class="font-bold text-gray-800 mb-2">Túi Hộp Phối Màu (BST Hạ)</h4>
              <p class="text-gray-600 text-sm mb-3">Quai phối màu nổi bật, thiết kế hiện đại</p>
              <div class="flex justify-between items-center">
                <span class="text-red-600 font-bold">Từ 45k-85k</span>
                <span class="text-gray-500 text-sm">MOQ: 50 túi</span>
              </div>
            </div>
          </div>

          <!-- Túi Hộp Đơn Giản -->
          <div class="gallery-item bg-white rounded-2xl shadow-lg overflow-hidden group" data-category="box">
            <div class="relative aspect-square">
              <img
                src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-day-in-logo/tui-vai-day-quai-phoi-mau-loma-bag-2.jpg"
                alt="Túi hộp đơn giản"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
              <div class="absolute top-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                💼 Tiện dụng
              </div>
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="absolute bottom-4 left-4 right-4 text-white">
                  <h4 class="text-lg font-bold mb-1">Túi Hộp Phối Màu (BST Hạ)</h4>
                  <p class="text-sm text-white/90 mb-2">Phù hợp: Mọi ngành hàng, đa năng</p>
                  <div class="flex items-center text-yellow-400 text-sm">
                    <span class="mr-2">⭐⭐⭐⭐⭐</span>
                    <span class="text-white">400+ shop đã chọn</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="p-6">
              <h4 class="font-bold text-gray-800 mb-2">Túi Hộp Phối Màu (BST Hạ)</h4>
              <p class="text-gray-600 text-sm mb-3">Form chuẩn, dễ đựng đồ, giá hợp lý</p>
              <div class="flex justify-between items-center">
                <span class="text-red-600 font-bold">Từ 45k-85k</span>
                <span class="text-gray-500 text-sm">MOQ: 50 túi</span>
              </div>
            </div>
          </div>

          <!-- Túi Premium Đặc Biệt -->
          <div class="gallery-item bg-white rounded-2xl shadow-lg overflow-hidden group" data-category="premium">
            <div class="relative aspect-square">
              <img
                src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-day-in-logo/tui-vai-day-quai-phoi-mau-loma-bag-1.jpg"
                alt="Túi premium đặc biệt"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
              <div class="absolute top-4 left-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                💎 Limited
              </div>
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="absolute bottom-4 left-4 right-4 text-white">
                  <h4 class="text-lg font-bold mb-1">Túi Premium Đặc Biệt</h4>
                  <p class="text-sm text-white/90 mb-2">Phù hợp: Thương hiệu cao cấp, sự kiện</p>
                  <div class="flex items-center text-yellow-400 text-sm">
                    <span class="mr-2">⭐⭐⭐⭐⭐</span>
                    <span class="text-white">100+ shop đã chọn</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="p-6">
              <h4 class="font-bold text-gray-800 mb-2">Túi Hộp Phối Màu (BST Hạ)</h4>
              <p class="text-gray-600 text-sm mb-3">Chất liệu cao cấp, hoàn thiện tỉ mỉ</p>
              <div class="flex justify-between items-center">
                <span class="text-red-600 font-bold">Từ 45k-85k</span>
                <span class="text-gray-500 text-sm">MOQ: 50 túi</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Quality Showcase -->
        <div class="bg-white rounded-2xl p-8 mb-8 shadow-lg">
          <div class="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 class="text-2xl font-bold text-gray-800 mb-4">
                🎯 Chất Lượng In Ấn Sắc Nét
              </h3>
              <div class="space-y-3">
                <div class="flex items-center">
                  <span class="text-green-500 mr-3">✅</span>
                  <span class="text-gray-700">Công nghệ in lụa cao cấp, màu sắc bền đẹp</span>
                </div>
                <div class="flex items-center">
                  <span class="text-green-500 mr-3">✅</span>
                  <span class="text-gray-700">Logo sắc nét, không bong tróc sau nhiều lần giặt</span>
                </div>
                <div class="flex items-center">
                  <span class="text-green-500 mr-3">✅</span>
                  <span class="text-gray-700">Có thể in 1 mặt hoặc 2 mặt theo yêu cầu</span>
                </div>
                <div class="flex items-center">
                  <span class="text-green-500 mr-3">✅</span>
                  <span class="text-gray-700">Hỗ trợ in đa màu, màu chuyển, hình ảnh phức tạp</span>
                </div>
              </div>
            </div>
            <div class="relative">
              <img
                src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-day-in-logo/tui-vai-day-quai-phoi-mau-loma-bag-2.jpg"
                alt="Chất lượng in ấn"
                class="w-full h-64 object-cover rounded-xl"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-xl"></div>
              <div class="absolute bottom-4 left-4 text-white">
                <h4 class="font-bold">Chất lượng in ấn cao cấp</h4>
                <p class="text-sm text-white/90">Sắc nét từng chi tiết</p>
              </div>
            </div>
          </div>
        </div>

        <!-- CTA -->
        <div class="text-center">
          <a
            href="#form-7uTqf4pFme"
            class="cta-button bg-red-500 text-white font-bold py-4 px-8 rounded-full text-lg hover:bg-red-600 inline-block shadow-xl"
          >
            🎨 Thiết Kế Túi Cho Tôi Ngay
          </a>
        </div>
      </div>
    </section>

    <!-- Video Modal -->
    <div
      id="video-modal"
      class="modal fixed inset-0 bg-black bg-opacity-80 items-center justify-center z-50"
      style="display: none"
    >
      <div class="w-11/12 md:w-3/4 lg:w-2/3 max-w-4xl relative">
        <button
          class="close-modal-btn absolute -top-10 right-0 w-10 h-10 text-white text-4xl"
        >
          &times;
        </button>
        <div id="video-player" class="aspect-video"></div>
      </div>
    </div>

    <!-- Zalo Sticky Button -->
    <a href="https://zalo.me/0938069715" target="_blank" class="zalo-sticky">
      <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma//zalo-icon.png" alt="Zalo" class="w-8 h-8">
    </a>

    <!-- Scripts -->
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Category filter functionality
        const categoryTabs = document.querySelectorAll('.category-tab');
        const galleryItems = document.querySelectorAll('.gallery-item');

        categoryTabs.forEach(tab => {
          tab.addEventListener('click', function() {
            const category = this.dataset.category;

            // Update active tab
            categoryTabs.forEach(t => {
              t.classList.remove('active', 'bg-red-500', 'text-white');
              t.classList.add('bg-white', 'text-gray-700', 'border', 'border-gray-300');
            });
            this.classList.add('active', 'bg-red-500', 'text-white');
            this.classList.remove('bg-white', 'text-gray-700', 'border', 'border-gray-300');

            // Filter gallery items
            galleryItems.forEach(item => {
              if (category === 'all' || item.dataset.category === category) {
                item.style.display = 'block';
              } else {
                item.style.display = 'none';
              }
            });
          });
        });

        // --- VIDEO MODAL ---
        const videoModal = document.getElementById("video-modal");
        const videoPlayer = document.getElementById("video-player");

        document.querySelectorAll(".video-placeholder").forEach((item) => {
          item.addEventListener("click", (e) => {
            e.preventDefault();
            const videoId = item.dataset.videoId;

            if (videoPlayer) {
              videoPlayer.innerHTML = `<iframe class="w-full h-full" src="https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>`;
            }

            openModal(videoModal);
          });
        });

        // --- COMMON MODAL LOGIC ---
        function openModal(modal) {
          modal.style.display = "flex";
          modal.style.visibility = "visible";
          setTimeout(() => {
            modal.classList.add("show");
          }, 10);

          // Close modal when clicking outside content
          modal.addEventListener("click", (e) => {
            if (e.target === modal) {
              closeModal(modal);
            }
          });

          // Close modal when clicking close button
          modal.querySelectorAll(".close-modal-btn").forEach((btn) => {
            btn.addEventListener("click", () => {
              closeModal(modal);
            });
          });

          // Close modal with Escape key
          document.addEventListener("keydown", (e) => {
            if (e.key === "Escape") {
              closeModal(modal);
            }
          });
        }

        function closeModal(modal) {
          modal.classList.remove("show");
          modal.classList.add("hide");
          setTimeout(() => {
            modal.style.display = "none";
            modal.style.visibility = "hidden";
            modal.classList.remove("hide");

            // Stop video when closing
            if (modal.id === "video-modal") {
              if (videoPlayer) {
                videoPlayer.innerHTML = "";
              }
            }
          }, 300);
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
          anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
              target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            }
          });
        });
      });
    </script>
  </body>
</html>
