<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loma Bag - Túi Vải In Logo Giúp Tăng 15-30% Tỷ Lệ Chốt Đơn</title>
    <meta name="description" content="Xưởng sản xuất túi vải in logo từ 50 túi. Giúp shop online tăng tỷ lệ chốt đơn, tăng giá trị đơn hàng. Miễn phí thiết kế mockup, cọc 500k hoàn lại 100%.">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- SwiperJS for Gallery Slider -->
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />

    <style>
        /* Custom Styles */
        html {
            scroll-behavior: smooth;
        }
        body {
            font-family: 'Be Vietnam Pro', sans-serif;
            background-color: #f9fafb;
            line-height: 1.7;
        }
        h1, h2, h3, h4, h5, h6 {
            line-height: 1.4;
        }
        .cta-button {
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .cta-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }
        .section-title {
            position: relative;
            padding-bottom: 1rem;
        }
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: #EF4444;
            border-radius: 2px;
        }

        /* Enhanced Hero Section */
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }
        .hero-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        /* Typing animation */
        .typed-cursor {
            opacity: 1;
            animation: blink 0.7s infinite;
        }
        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0; }
            100% { opacity: 1; }
        }

        /* Floating animation */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .float-animation {
            animation: float 3s ease-in-out infinite;
        }
        
        /* Modal */
        .modal {
            transition: opacity 0.3s ease;
            z-index: 9999;
        }
        .modal.show {
            display: flex !important;
            opacity: 1 !important;
        }
        .modal.hide {
            opacity: 0 !important;
        }
        .video-placeholder {
            aspect-ratio: 16 / 9;
            background-color: #111827;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.75rem;
            cursor: pointer;
            overflow: hidden;
            position: relative;
        }
        .play-button {
            width: 80px;
            height: 80px;
            background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            border: 2px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease, background-color 0.2s ease;
            z-index: 10;
        }
        .play-button:hover {
            transform: scale(1.1);
            background-color: rgba(239, 68, 68, 0.8);
        }
        .video-placeholder img {
            transition: transform 0.5s ease;
        }
        .video-placeholder:hover img {
            transform: scale(1.05);
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- HEADER -->
    <header class="bg-white/90 backdrop-blur-lg fixed top-0 left-0 right-0 z-50 shadow-sm">
        <div class="container mx-auto px-6 py-4 flex justify-between items-center">
            <a href="#" class="flex items-center">
              <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma//logo-full.png" alt="Loma Bag Logo" class="h-10">
            </a>
            <a href="#form-bao-gia" class="cta-button bg-red-500 text-white font-bold py-2 px-6 rounded-full hover:bg-red-600 hidden sm:inline-block">
                Nhận Báo Giá Chính Xác
            </a>
        </div>
    </header>

    <!-- HERO SECTION -->
    <section class="hero-gradient text-white pt-32 pb-20 text-center relative">
        <div class="container mx-auto px-6 relative z-10">
            <!-- Floating icons -->
            <div class="absolute top-10 left-10 float-animation hidden lg:block">
                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <span class="text-2xl">🛍️</span>
                </div>
            </div>
            <div class="absolute top-20 right-20 float-animation hidden lg:block" style="animation-delay: 1s;">
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <span class="text-xl">📈</span>
                </div>
            </div>

            <div class="max-w-4xl mx-auto">
                <h2 class="text-4xl md:text-6xl font-extrabold leading-tight mb-6">
                    Đang chạy Ads mà khách cứ "xem rồi thoát"?<br>
                    <span class="text-yellow-300">Thêm 1 món quà nhỏ để...</span><br>
                    <span id="typing-effect" class="text-red-300"></span>
                </h2>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20">
                    <p class="text-xl md:text-2xl font-semibold text-yellow-100 mb-4">
                        💡 Sự thật: Bạn đã chi tiền cho Ads để khách click vào
                    </p>
                    <p class="text-lg md:text-xl text-white/90">
                        Nhưng <strong class="text-yellow-300">90% khách xem rồi thoát</strong> mà không mua gì.
                        Một chiếc <strong class="text-red-300">túi vải in logo làm quà tặng</strong> sẽ là "cú hích" cuối cùng
                        khiến họ <strong class="text-green-300">chốt đơn ngay lập tức!</strong>
                    </p>
                </div>
                <div class="flex flex-col sm:flex-row justify-center items-center gap-4">
                     <a href="#form-bao-gia" class="cta-button bg-red-500 text-white font-bold py-4 px-8 rounded-full text-lg hover:bg-red-600 inline-block w-full sm:w-auto">
                        🎁 Nhận Báo Giá Ngay
                    </a>
                    <a href="#case-study" class="cta-button bg-white/20 backdrop-blur-sm text-white border border-white/30 font-bold py-4 px-8 rounded-full text-lg hover:bg-white/30 inline-block w-full sm:w-auto">
                        📊 Xem Case Thực Tế
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- WHY CHOOSE BAG SECTION -->
    <section class="py-16 md:py-24 bg-gradient-to-br from-blue-50 to-indigo-50">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="text-center mb-12">
                <h3 class="section-title text-2xl md:text-4xl font-bold">🎯 Tại Sao Túi Vải In Logo Lại Hiệu Quả Đến Vậy?</h3>
                <p class="mt-4 text-gray-600 max-w-3xl mx-auto text-base md:text-lg">
                    Không phải ngẫu nhiên mà các thương hiệu lớn đều sử dụng túi vải làm quà tặng. Đây là chiến lược marketing thông minh!
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                <div class="bg-white p-6 rounded-xl shadow-lg text-center border border-blue-100">
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl text-white">🎁</span>
                    </div>
                    <h4 class="font-bold text-lg mb-3 text-gray-800">Tạo Cảm Giác Được Tặng Quà</h4>
                    <p class="text-gray-600 text-sm">Khách hàng cảm thấy được "ưu đãi đặc biệt" → Tăng thiện cảm với thương hiệu → Dễ chốt đơn hơn</p>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-lg text-center border border-green-100">
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl text-white">💰</span>
                    </div>
                    <h4 class="font-bold text-lg mb-3 text-gray-800">Tăng Giá Trị Cảm Nhận</h4>
                    <p class="text-gray-600 text-sm">Đơn hàng 500k + túi đẹp = Cảm giác "được nhiều hơn trả" → Khách sẵn sàng mua thêm để đủ điều kiện</p>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-lg text-center border border-purple-100">
                    <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl text-white">📢</span>
                    </div>
                    <h4 class="font-bold text-lg mb-3 text-gray-800">Quảng Bá Miễn Phí</h4>
                    <p class="text-gray-600 text-sm">Khách dùng túi đi chợ, đi làm = Logo thương hiệu được nhìn thấy hàng trăm lần mỗi ngày</p>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-lg text-center border border-yellow-100">
                    <div class="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl text-white">🔄</span>
                    </div>
                    <h4 class="font-bold text-lg mb-3 text-gray-800">Khách Hàng Quay Lại</h4>
                    <p class="text-gray-600 text-sm">Túi nhắc nhở thương hiệu mỗi khi sử dụng → Tăng tỷ lệ khách cũ mua lại → Giảm chi phí ads</p>
                </div>
            </div>

            <div class="bg-white p-8 rounded-2xl shadow-xl border border-gray-200 max-w-4xl mx-auto">
                <div class="text-center mb-6">
                    <h4 class="text-xl md:text-2xl font-bold text-gray-800 mb-3">💡 Công Thức Thành Công Đã Được Chứng Minh</h4>
                </div>
                <div class="grid md:grid-cols-3 gap-6 text-center">
                    <div class="p-4 bg-red-50 rounded-lg">
                        <div class="text-3xl font-bold text-red-600 mb-2">Bước 1</div>
                        <p class="text-gray-700 font-semibold">Đặt điều kiện: "Đơn từ 500k tặng túi đẹp"</p>
                    </div>
                    <div class="p-4 bg-blue-50 rounded-lg">
                        <div class="text-3xl font-bold text-blue-600 mb-2">Bước 2</div>
                        <p class="text-gray-700 font-semibold">Khách mua thêm để đủ 500k nhận túi</p>
                    </div>
                    <div class="p-4 bg-green-50 rounded-lg">
                        <div class="text-3xl font-bold text-green-600 mb-2">Kết Quả</div>
                        <p class="text-gray-700 font-semibold">Tỷ lệ chốt ↗️ Giá trị đơn ↗️ Lợi nhuận ↗️</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
